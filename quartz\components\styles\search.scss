@use "../../styles/variables.scss" as *;

.search {
  min-width: fit-content;
  max-width: 360px;
  @media all and ($mobile) {
    flex-grow: 0;
    flex-shrink: 0;
    max-width: 100%;
    width: 100%;
  }

  & > .search-button {
    background-color: var(--lightgray);
    border: none;
    border-radius: 4px;
    font-family: inherit;
    font-size: inherit;
    height: 2rem;
    padding: 0;
    display: flex;
    align-items: center;
    text-align: inherit;
    cursor: pointer;
    white-space: nowrap;
    width: 100%;
    justify-content: space-between;

    // 移动端搜索按钮优化
    @media all and ($mobile) {
      height: 2.5rem;
      min-height: 2.5rem;
      max-height: 2.5rem;
    }

    & > p {
      display: inline;
      padding: 0 1rem;
    }

    & svg {
      cursor: pointer;
      width: 18px;
      min-width: 18px;
      margin: 0 0.5rem;

      .search-path {
        stroke: var(--darkgray);
        stroke-width: 2px;
        transition: stroke 0.5s ease;
      }
    }
  }

  & > #search-container {
    position: fixed;
    contain: layout;
    z-index: 999;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
    height: 100vh;
    overflow-y: auto;
    display: none;
    backdrop-filter: blur(4px);
    background-color: rgba(0, 0, 0, 0.3);
    box-sizing: border-box;

    &.active {
      display: inline-block;
    }

    & > #search-space {
      width: 65%;
      margin-top: 12vh;
      margin-left: auto;
      margin-right: auto;

      @media all and not ($desktop) {
        width: 90%;
      }

      & > * {
        width: 100%;
        border-radius: 7px;
        background: var(--light);
        box-shadow:
          0 14px 50px rgba(27, 33, 48, 0.12),
          0 10px 30px rgba(27, 33, 48, 0.16);
        margin-bottom: 2em;
      }

      & > input {
        box-sizing: border-box;
        padding: 0.5em 1em;
        font-family: var(--bodyFont);
        color: var(--dark);
        font-size: 1.1em;
        border: 1px solid var(--lightgray);

        &:focus {
          outline: none;
        }
      }

      & > #search-layout {
        display: none;
        flex-direction: row;
        border: 1px solid var(--lightgray);
        flex: 0 0 100%;
        box-sizing: border-box;

        &.display-results {
          display: flex;
        }

        &[data-preview] > #results-container {
          flex: 0 0 min(30%, 450px);
        }

        @media all and not ($tablet) {
          &[data-preview] {
            & .result-card > p.preview {
              display: none;
            }

            & > div {
              &:first-child {
                border-right: 1px solid var(--lightgray);
                border-top-right-radius: unset;
                border-bottom-right-radius: unset;
              }

              &:last-child {
                border-top-left-radius: unset;
                border-bottom-left-radius: unset;
              }
            }
          }
        }

        & > div {
          height: calc(75vh - 12vh);
          border-radius: 5px;
        }

        @media all and ($tablet) {
          & > #preview-container {
            display: none !important;
          }

          &[data-preview] > #results-container {
            width: 100%;
            height: auto;
            flex: 0 0 100%;
          }
        }

        & .highlight {
          background: color-mix(in srgb, var(--tertiary) 60%, rgba(255, 255, 255, 0));
          border-radius: 5px;
          scroll-margin-top: 2rem;
        }

        & > #preview-container {
          display: block;
          overflow: hidden;
          font-family: inherit;
          color: var(--dark);
          line-height: 1.5em;
          font-weight: $normalWeight;
          overflow-y: auto;
          padding: 0 2rem;

          & .preview-inner {
            margin: 0 auto;
            width: min($pageWidth, 100%);
          }

          a[role="anchor"] {
            background-color: transparent;
          }
        }

        & > #results-container {
          overflow-y: auto;

          & .result-card {
            overflow: hidden;
            padding: 1em;
            cursor: pointer;
            transition: background 0.2s ease;
            border-bottom: 1px solid var(--lightgray);
            width: 100%;
            display: block;
            box-sizing: border-box;

            // normalize card props
            font-family: inherit;
            font-size: 100%;
            line-height: 1.15;
            margin: 0;
            text-transform: none;
            text-align: left;
            outline: none;
            font-weight: inherit;

            &:hover,
            &:focus,
            &.focus {
              background: var(--lightgray);
            }

            & > h3 {
              margin: 0;
            }

            & > ul.tags {
              margin-top: 0.45rem;
              margin-bottom: 0;
            }

            & > ul > li > p {
              border-radius: 8px;
              background-color: var(--highlight);
              padding: 0.2rem 0.4rem;
              margin: 0 0.1rem;
              line-height: 1.4rem;
              font-weight: $boldWeight;
              color: var(--secondary);

              &.match-tag {
                color: var(--tertiary);
              }
            }

            & > p {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }
}
