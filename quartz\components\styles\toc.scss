@use "../../styles/variables.scss" as *;

.toc {
  display: flex;
  flex-direction: column;
  padding: 1rem 0; // 增加容器的上下内边距

  &.desktop-only {
    max-height: 40%;
  }
}

@media all and not ($mobile) {
  .toc {
    display: flex;
  }
}

button#toc {
  background-color: transparent;
  border: none;
  text-align: left;
  cursor: pointer;
  padding: 0;
  color: var(--dark);
  display: flex;
  align-items: center;

  & h3 {
    font-size: 1rem;
    display: inline-block;
    margin: 0 0 0.75rem 0; // 增加标题的下边距
  }

  & .fold {
    margin-left: 0.5rem;
    transition: transform 0.3s ease;
    opacity: 0.8;
  }

  &.collapsed .fold {
    transform: rotateZ(-90deg);
  }
}

#toc-content {
  list-style: none;
  overflow: hidden;
  overflow-y: auto;
  max-height: 100%;
  transition:
    max-height 0.35s ease,
    visibility 0s linear 0s;
  position: relative;
  visibility: visible;

  &.collapsed {
    max-height: 0;
    transition:
      max-height 0.35s ease,
      visibility 0s linear 0.35s;
    visibility: hidden;
  }

  &.collapsed > .overflow::after {
    opacity: 0;
  }

  & ul {
    list-style: none;
    margin: 0.75rem 0; // 增加列表的上下边距
    padding: 0;
    & > li > a {
      color: var(--dark);
      opacity: 0.35;
      transition:
        0.5s ease opacity,
        0.3s ease color;
      &.in-view {
        opacity: 0.75;
      }
    }
  }
  > ul.overflow {
    max-height: none;
    width: 100%;
  }

  @for $i from 0 through 6 {
    & .depth-#{$i} {
      padding-left: calc(1rem * #{$i});
    }
  }
}
