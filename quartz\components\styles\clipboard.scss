.clipboard-button {
  position: absolute;
  display: flex;
  float: right;
  right: 0;
  padding: 0.4rem;
  margin: 0.3rem;
  color: var(--gray);
  border-color: var(--dark);
  background-color: var(--light);
  border: 1px solid;
  border-radius: 5px;
  opacity: 0;
  transition: 0.2s;

  & > svg {
    fill: var(--light);
    filter: contrast(0.3);
  }

  &:hover {
    cursor: pointer;
    border-color: var(--secondary);
  }

  &:focus {
    outline: 0;
  }
}

pre {
  &:hover > .clipboard-button {
    opacity: 1;
    transition: 0.2s;
  }
}
