name: Docker build & push image

on:
  push:
    branches: [v4]
    tags: ["v*"]
  pull_request:
    branches: [v4]
    paths:
      - .github/workflows/docker-build-push.yaml
      - quartz/**
  workflow_dispatch:

jobs:
  build:
    if: ${{ github.repository == 'jackyzha0/quartz' }} # Comment this out if you want to publish your own images on a fork!
    runs-on: ubuntu-latest
    steps:
      - name: Set lowercase repository owner environment variable
        run: |
          echo "OWNER_LOWERCASE=${OWNER,,}" >> ${GITHUB_ENV}
        env:
          OWNER: "${{ github.repository_owner }}"
      - uses: actions/checkout@v4
        with:
          fetch-depth: 1
      - name: Inject slug/short variables
        uses: rlespinasse/github-slug-action@v4.4.1
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          install: true
          driver-opts: |
            image=moby/buildkit:master
            network=host
      - name: Install cosign
        if: github.event_name != 'pull_request'
        uses: sigstore/cosign-installer@v3.7.0
      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        if: github.event_name != 'pull_request'
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata tags and labels on PRs
        if: github.event_name == 'pull_request'
        id: meta-pr
        uses: docker/metadata-action@v5
        with:
          images: ghcr.io/${{ env.OWNER_LOWERCASE }}/quartz
          tags: |
            type=raw,value=sha-${{ env.GITHUB_SHA_SHORT }}
          labels: |
            org.opencontainers.image.source="https://github.com/${{ github.repository_owner }}/quartz"
      - name: Extract metadata tags and labels for main, release or tag
        if: github.event_name != 'pull_request'
        id: meta
        uses: docker/metadata-action@v5
        with:
          flavor: |
            latest=auto
          images: ghcr.io/${{ env.OWNER_LOWERCASE }}/quartz
          tags: |
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=semver,pattern={{major}}.{{minor}}.{{patch}}
            type=raw,value=latest,enable=${{ github.ref == format('refs/heads/{0}', github.event.repository.default_branch) }}
            type=raw,value=sha-${{ env.GITHUB_SHA_SHORT }}
          labels: |
            maintainer=${{ github.repository_owner }}
            org.opencontainers.image.source="https://github.com/${{ github.repository_owner }}/quartz"

      - name: Build and push Docker image
        id: build-and-push
        uses: docker/build-push-action@v6
        with:
          push: ${{ github.event_name != 'pull_request' }}
          build-args: |
            GIT_SHA=${{ env.GITHUB_SHA }}
            DOCKER_LABEL=sha-${{ env.GITHUB_SHA_SHORT }}
          tags: ${{ steps.meta.outputs.tags || steps.meta-pr.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels || steps.meta-pr.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha
