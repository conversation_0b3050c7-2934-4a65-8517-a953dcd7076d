# Quartz 发布到 GitHub Pages 完整教程

## 📖 教程概述

本教程将指导你如何将 Quartz 静态网站发布到 GitHub Pages，实现自动化部署。完成后，每次更新内容都会自动发布到你的个人网站。

## 🎯 最终效果

- ✅ 拥有一个 `用户名.github.io` 的个人网站
- ✅ 每次推送代码自动更新网站
- ✅ 支持自定义域名（可选）
- ✅ 完全免费托管

## 📋 前置准备

### 1. 环境要求
- [x] 已安装 Git
- [x] 已安装 Node.js (v20+)
- [x] 拥有 GitHub 账号
- [x] 已有 Quartz 项目

### 2. 创建 GitHub Pages 仓库
1. 登录 GitHub，点击右上角 "+" → "New repository"
2. 仓库名必须是：`你的用户名.github.io`
   - 例如：`AINightCoder.github.io`
3. 设置为 Public（公开）
4. 不要勾选任何初始化选项
5. 点击 "Create repository"

## 🔧 配置步骤

### 第一步：更新 Quartz 配置

打开 `quartz.config.ts` 文件，修改 `baseUrl` 配置：

```typescript
const config: QuartzConfig = {
  configuration: {
    // 其他配置...
    baseUrl: "你的用户名.github.io",  // 改为你的GitHub Pages域名
    // 其他配置...
  },
}
```

### 第二步：创建 GitHub Actions 工作流

在项目根目录创建 `.github/workflows/deploy.yml` 文件：

```yaml
name: Deploy Quartz site to GitHub Pages

on:
  push:
    branches:
      - main
      - master
  workflow_dispatch:

permissions:
  contents: read
  pages: write
  id-token: write

concurrency:
  group: "pages"
  cancel-in-progress: false

jobs:
  build:
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'npm'

      - name: Install Dependencies
        run: npm ci

      - name: Build Quartz
        run: npx quartz build

      - name: Setup Pages
        uses: actions/configure-pages@v4

      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: public

  deploy:
    needs: build
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
```

### 第三步：配置 Git 远程仓库

在项目目录中执行以下命令：

```bash
# 添加 GitHub Pages 仓库作为远程仓库
git remote add github-pages **************:你的用户名/你的用户名.github.io.git

# 验证远程仓库配置
git remote -v
```

### 第四步：推送代码到 GitHub Pages 仓库

```bash
# 提交所有更改
git add .
git commit -m "配置 GitHub Pages 部署"

# 推送到 GitHub Pages 仓库
git push github-pages main:main
```

## ⚙️ GitHub 仓库设置

### 1. 启用 GitHub Pages

1. 打开你的 GitHub Pages 仓库页面
2. 点击 "Settings" 标签
3. 在左侧菜单找到 "Pages"
4. 在 "Source" 部分选择 "GitHub Actions"
5. 保存设置

### 2. 配置 Actions 权限

1. 在 Settings 页面，找到 "Actions" → "General"
2. 确保以下设置：
   - Actions permissions: "Allow all actions and reusable workflows"
   - Workflow permissions: "Read and write permissions"
   - 勾选 "Allow GitHub Actions to create and approve pull requests"

## 🚀 部署验证

### 1. 检查部署状态
1. 访问仓库的 "Actions" 标签页
2. 查看最新的工作流运行状态
3. 确保显示绿色的 ✅ 成功标志

### 2. 访问网站
- 等待 5-10 分钟让 GitHub 处理部署
- 访问 `https://你的用户名.github.io`
- 确认网站内容正确显示

## 📝 日常使用流程

配置完成后，你的日常工作流程：

1. **编辑内容**：在 `content/` 目录下编辑 Markdown 文件
2. **本地预览**：运行 `npx quartz build --serve` 预览效果
3. **提交推送**：
   ```bash
   git add .
   git commit -m "更新内容描述"
   git push github-pages main:main
   ```
4. **自动部署**：GitHub Actions 自动构建并部署网站

## 🔧 高级配置

### 自定义域名（可选）

如果你有自己的域名：

1. 在仓库根目录创建 `CNAME` 文件，内容为你的域名
2. 在域名 DNS 设置中添加 CNAME 记录指向 `你的用户名.github.io`
3. 在 GitHub Pages 设置中配置自定义域名

### 多仓库管理

如果你想保持源码和发布分离：

```bash
# 保持原仓库用于开发
git push origin main

# 同时推送到 GitHub Pages 仓库用于发布
git push github-pages main:main
```

## ❗ 常见问题

### Q: Actions 运行失败怎么办？
A: 检查以下几点：
- GitHub Pages 是否已启用
- Actions 权限是否正确配置
- 工作流文件语法是否正确

### Q: 网站显示 404 错误？
A: 可能原因：
- GitHub Pages 还在处理中，等待几分钟
- baseUrl 配置不正确
- 仓库名不符合 `用户名.github.io` 格式

### Q: 如何更新网站内容？
A: 只需要：
1. 编辑 `content/` 目录下的文件
2. 提交并推送到 GitHub Pages 仓库
3. 等待自动部署完成

### Q: 出现两个 Actions 同时运行怎么办？
A: 这通常是因为有多个工作流文件：
1. 检查 `.github/workflows/` 目录
2. 删除多余的工作流文件
3. 只保留一个有效的部署工作流

### Q: 如何本地测试？
A: 使用以下命令：
```bash
# 构建网站
npx quartz build

# 本地预览
npx quartz build --serve
```

## 🎉 完成

恭喜！你已经成功配置了 Quartz 到 GitHub Pages 的自动化部署。现在你可以专注于创作内容，网站会自动更新发布。

## 📚 相关资源

- [Quartz 官方文档](https://quartz.jzhao.xyz/)
- [GitHub Pages 文档](https://docs.github.com/en/pages)
- [GitHub Actions 文档](https://docs.github.com/en/actions)

---

**提示**：如果遇到问题，可以查看 GitHub Actions 的详细日志来诊断问题。记住，第一次部署可能需要等待几分钟才能看到效果。