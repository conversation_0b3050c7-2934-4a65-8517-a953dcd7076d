@use "../../styles/variables.scss" as *;

.explorer {
  display: flex;
  flex-direction: column;
  overflow-y: hidden;
  &.desktop-only {
    @media all and not ($mobile) {
      display: flex;
    }
  }
  /*&:after {
    pointer-events: none;
    content: "";
    width: 100%;
    height: 50px;
    position: absolute;
    left: 0;
    bottom: 0;
    opacity: 1;
    transition: opacity 0.3s ease;
    background: linear-gradient(transparent 0px, var(--light));
  }*/
}

button#explorer {
  background-color: transparent;
  border: none;
  text-align: left;
  cursor: pointer;
  padding: 0;
  color: var(--dark);
  display: flex;
  align-items: center;

  & h2 {
    font-size: 0.9rem;
    display: inline-block;
    margin: 0;
  }

  & .fold {
    margin-left: 0.5rem;
    transition: transform 0.3s ease;
    opacity: 0.8;
  }

  &.collapsed .fold {
    transform: rotateZ(-90deg);
  }
}

.folder-outer {
  display: grid;
  grid-template-rows: 0fr;
  transition: grid-template-rows 0.3s ease-in-out;
}

.folder-outer.open {
  grid-template-rows: 1fr;
}

.folder-outer > ul {
  overflow: hidden;
}

#explorer-content {
  list-style: none;
  overflow: hidden;
  overflow-y: auto;
  max-height: 100%;
  transition:
    max-height 0.35s ease,
    visibility 0s linear 0s;
  margin-top: 0.5rem;
  visibility: visible;

  &.collapsed {
    max-height: 0;
    transition:
      max-height 0.35s ease,
      visibility 0s linear 0.35s;
    visibility: hidden;
  }

  & ul {
    list-style: none;
    margin: 0.08rem 0;
    padding: 0;
    transition:
      max-height 0.35s ease,
      transform 0.35s ease,
      opacity 0.2s ease;
    & li > a {
      color: var(--dark);
      opacity: 0.75;
      pointer-events: all;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: block;
      font-size: 0.85rem;
      line-height: 1.4rem;
    }
  }
  > #explorer-ul {
    max-height: none;
  }
}

svg {
  pointer-events: all;

  & > polyline {
    pointer-events: none;
  }
}

.folder-container {
  flex-direction: row;
  display: flex;
  align-items: center;
  justify-content: space-between;
  user-select: none;

  & div > a {
    color: var(--secondary);
    font-family: var(--headerFont);
    font-size: 0.85rem;
    font-weight: $semiBoldWeight;
    line-height: 1.4rem;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 100%;
  }

  & div > a:hover {
    color: var(--tertiary);
  }

  & div > button {
    color: var(--dark);
    background-color: transparent;
    border: none;
    text-align: left;
    cursor: pointer;
    padding-left: 0;
    padding-right: 0;
    display: flex;
    align-items: center;
    font-family: var(--headerFont);

    & span {
      font-size: 0.85rem;
      display: inline-block;
      color: var(--secondary);
      font-weight: $semiBoldWeight;
      margin: 0;
      line-height: 1.4rem;
      pointer-events: none;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 100%;
    }
  }
}

.folder-icon {
  margin-left: 8px;
  margin-right: 0;
  color: var(--gray);
  cursor: pointer;
  transition: transform 0.3s ease, color 0.2s ease;
  backface-visibility: visible;
  opacity: 0.7;
}

li:has(> .folder-outer:not(.open)) > .folder-container > svg {
  transform: rotate(-90deg);
}

.folder-icon:hover {
  color: var(--secondary);
  opacity: 1;
}

.no-background::after {
  background: none !important;
}

#explorer-end {
  // needs height so IntersectionObserver gets triggered
  height: 4px;
  // remove default margin from li
  margin: 0;
}
