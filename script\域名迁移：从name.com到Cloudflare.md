# 域名迁移：从 Name.com 到 Cloudflare 完整教程

## 📖 教程概述

本教程将指导你如何将在 Name.com 购买的域名迁移到 Cloudflare，享受更快的DNS解析、免费的CDN服务和更强大的安全防护。

## 🎯 迁移优势

**为什么选择 Cloudflare？**
- 🚀 **更快的DNS解析** - 全球最快的DNS服务
- 🛡️ **免费安全防护** - DDoS防护、SSL证书
- 🌐 **免费CDN** - 全球内容分发网络
- 📊 **详细分析** - 流量统计和安全报告
- 💰 **成本更低** - 域名续费价格通常更便宜
- 🔧 **强大功能** - 页面规则、Workers等高级功能

## ⚠️ 重要提醒

**迁移前必读：**
- ✅ 域名注册满60天后才能转移
- ✅ 确保域名未被锁定
- ✅ 准备好域名的授权码（EPP Code）
- ✅ 确保域名联系信息是最新的
- ⏰ 整个过程可能需要5-7天完成

## 📋 前置准备

### 1. 检查域名状态
在开始前，确认以下信息：
- [x] 域名注册时间超过60天
- [x] 域名状态为"Active"
- [x] 域名未被锁定（Domain Lock关闭）
- [x] 联系邮箱可以正常接收邮件

### 2. 准备账户
- [x] Name.com 账户（当前域名注册商）
- [x] Cloudflare 账户（如果没有需要注册）
- [x] 可以接收邮件的邮箱

## 🔧 迁移步骤

### 第一步：在 Name.com 准备域名转移

#### 1.1 登录 Name.com
1. 访问 [Name.com](https://www.name.com)
2. 点击右上角 "Sign In" 登录账户
3. 进入 "My Account" → "My Domains"

#### 1.2 解锁域名
1. 找到要转移的域名，点击域名进入管理页面
2. 在 "Settings" 或 "Domain Lock" 部分
3. 将 "Domain Lock" 设置为 **OFF（关闭）**
4. 确认更改

#### 1.3 获取授权码（EPP Code）
1. 在域名管理页面找到 "Auth Code" 或 "EPP Code"
2. 点击 "Get Auth Code" 或 "Request Auth Code"
3. 授权码会发送到域名注册邮箱
4. **重要：保存好这个授权码，转移时需要用到**

#### 1.4 更新联系信息（如需要）
1. 确保域名的联系邮箱是当前可用的
2. 在 "Contact Information" 中更新信息
3. 保存更改

### 第二步：在 Cloudflare 添加域名

#### 2.1 登录 Cloudflare
1. 访问 [Cloudflare](https://www.cloudflare.com)
2. 登录账户或注册新账户
3. 进入 Cloudflare 控制面板

#### 2.2 添加站点
1. 点击 "Add a Site" 按钮
2. 输入你的域名（例如：example.com）
3. 点击 "Add Site"

#### 2.3 选择计划
1. 选择 "Free" 计划（对大多数用户足够）
2. 点击 "Continue"

#### 2.4 DNS 记录扫描
1. Cloudflare 会自动扫描现有的 DNS 记录
2. 检查扫描结果，确保重要记录都被检测到
3. 如有遗漏，手动添加 DNS 记录
4. 点击 "Continue"

### 第三步：更改 DNS 服务器

#### 3.1 获取 Cloudflare DNS 服务器
Cloudflare 会显示两个 DNS 服务器地址，类似：
```
ns1.cloudflare.com
ns2.cloudflare.com
```
**记录下这两个地址**

#### 3.2 在 Name.com 更改 DNS
1. 回到 Name.com 的域名管理页面
2. 找到 "Nameservers" 或 "DNS Settings"
3. 选择 "Custom Nameservers"
4. 删除现有的 DNS 服务器
5. 添加 Cloudflare 的两个 DNS 服务器
6. 保存更改

#### 3.3 验证 DNS 更改
1. 回到 Cloudflare 控制面板
2. 点击 "Done, check nameservers"
3. 等待 DNS 传播（通常需要几分钟到24小时）

### 第四步：启动域名转移

#### 4.1 在 Cloudflare 启动转移
1. 在 Cloudflare 控制面板，找到域名
2. 点击域名进入管理页面
3. 在右侧菜单找到 "Registrar"
4. 点击 "Transfer Domain"

#### 4.2 输入授权码
1. 输入从 Name.com 获取的授权码（EPP Code）
2. 确认域名联系信息
3. 选择转移年限（通常会延长1年）
4. 确认转移费用

#### 4.3 确认转移
1. 检查所有信息无误
2. 点击 "Confirm Transfer"
3. 完成付款（如有费用）

### 第五步：确认转移

#### 5.1 邮件确认
1. 检查域名注册邮箱
2. 会收到来自 Name.com 的转移确认邮件
3. 点击邮件中的确认链接
4. 按照指示完成确认

#### 5.2 等待处理
1. 转移通常需要5-7天完成
2. 可以在 Cloudflare 控制面板查看转移状态
3. Name.com 也会显示转移进度

## 🔍 转移后配置

### 1. 验证域名转移成功
1. 在 Cloudflare 控制面板确认域名状态为 "Active"
2. 使用 WHOIS 查询确认注册商已变更为 Cloudflare
3. 确认网站正常访问

### 2. 配置 SSL/TLS
1. 在 Cloudflare 控制面板进入 "SSL/TLS"
2. 选择 "Full" 或 "Full (strict)" 模式
3. 启用 "Always Use HTTPS"

### 3. 优化设置
1. **Speed** → 启用 "Auto Minify"
2. **Caching** → 设置合适的缓存级别
3. **Security** → 根据需要调整安全级别

### 4. 设置页面规则（可选）
1. 进入 "Rules" → "Page Rules"
2. 创建重定向规则
3. 设置缓存规则

## ❗ 常见问题

### Q: 转移需要多长时间？
A: 通常需要5-7天，但可能因以下因素延长：
- 域名注册商的处理速度
- 邮件确认的及时性
- 节假日等因素

### Q: 转移期间网站会中断吗？
A: 不会。只要提前更改了DNS服务器，网站会正常运行。

### Q: 转移失败怎么办？
A: 常见原因和解决方法：
- **域名被锁定** → 在原注册商解锁
- **授权码错误** → 重新获取正确的授权码
- **联系信息不匹配** → 更新域名联系信息
- **域名注册不足60天** → 等待满60天后再转移

### Q: 转移费用是多少？
A: Cloudflare 的域名转移通常包含1年续费，价格透明无隐藏费用。

### Q: 可以取消转移吗？
A: 在转移完成前，可以在原注册商拒绝转移请求。

### Q: DNS 记录会丢失吗？
A: 不会。Cloudflare 会自动扫描并导入现有的DNS记录。

## ⚠️ 重要注意事项

### 1. 备份重要信息
转移前请备份：
- 当前的 DNS 记录
- 域名联系信息
- 重要的邮件设置

### 2. 时间规划
- 选择业务低峰期进行转移
- 避免在重要活动前转移域名
- 预留足够的时间处理可能的问题

### 3. 邮件监控
- 密切关注注册邮箱
- 及时响应确认邮件
- 检查垃圾邮件文件夹

### 4. 测试验证
转移完成后进行全面测试：
- 网站访问是否正常
- 邮件服务是否正常
- 子域名是否正常解析

## 🎉 转移完成后的优势

转移到 Cloudflare 后，你将享受到：

### 性能提升
- 🚀 全球CDN加速
- ⚡ 更快的DNS解析
- 📱 移动端优化

### 安全增强
- 🛡️ DDoS攻击防护
- 🔒 免费SSL证书
- 🚫 恶意流量过滤

### 成本节约
- 💰 透明的续费价格
- 🆓 免费的基础功能
- 📊 详细的使用统计

### 管理便利
- 🎛️ 统一的控制面板
- 📈 实时监控数据
- 🔧 丰富的配置选项

## 📚 相关资源

- [Cloudflare 官方文档](https://developers.cloudflare.com/)
- [域名转移指南](https://support.cloudflare.com/hc/en-us/articles/201720164)
- [DNS 设置教程](https://support.cloudflare.com/hc/en-us/articles/360019093151)
- [SSL/TLS 配置](https://support.cloudflare.com/hc/en-us/articles/200170416)

## 🎯 总结

域名从 Name.com 迁移到 Cloudflare 是一个相对简单但需要耐心的过程。按照本教程的步骤操作，你可以顺利完成迁移，并享受到 Cloudflare 提供的强大功能和优质服务。

**关键要点：**
1. 确保域名满足转移条件
2. 提前准备好授权码
3. 先更改DNS服务器，再启动域名转移
4. 耐心等待转移完成
5. 转移后进行全面测试

---

**提示**：如果在转移过程中遇到问题，可以联系 Cloudflare 客服获得帮助。记住，域名转移是一个标准流程，不要担心，按步骤操作即可成功完成。