import { Translation } from "./definition"

export default {
  propertyDefaults: {
    title: "Névtelen",
    description: "Nincs leí<PERSON>",
  },
  components: {
    callout: {
      note: "Jegyze<PERSON>",
      abstract: "Abstract",
      info: "Információ",
      todo: "<PERSON><PERSON><PERSON><PERSON>",
      tip: "Tipp",
      success: "<PERSON><PERSON>",
      question: "<PERSON><PERSON><PERSON><PERSON>",
      warning: "<PERSON>gyelmeztetés",
      failure: "<PERSON><PERSON>",
      danger: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      bug: "Bug",
      example: "<PERSON><PERSON><PERSON>",
      quote: "Id<PERSON><PERSON><PERSON>",
    },
    backlinks: {
      title: "Visszautalások",
      noBacklinksFound: "Nincs visszautal<PERSON>",
    },
    themeToggle: {
      lightMode: "Világos mód",
      darkMode: "<PERSON>ö<PERSON><PERSON> mód",
    },
    explorer: {
      title: "Fájlböngésző",
    },
    footer: {
      createdWith: "Készítve ezzel:",
    },
    graph: {
      title: "Grafikonnézet",
    },
    recentNotes: {
      title: "Legutó<PERSON><PERSON> jeg<PERSON>",
      seeRemainingMore: ({ remaining }) => `${remaining} tová<PERSON>i me<PERSON> →`,
    },
    transcludes: {
      transcludeOf: ({ targetSlug }) => `${targetSlug} áthivatkozása`,
      linkToOriginal: "Hivatkozás az eredetire",
    },
    search: {
      title: "Keresés",
      searchBarPlaceholder: "Keress valamire",
    },
    tableOfContents: {
      title: "Tartalomjegyzék",
    },
    contentMeta: {
      readingTime: ({ minutes }) => `${minutes} perces olvasás`,
    },
  },
  pages: {
    rss: {
      recentNotes: "Legutóbbi jegyzetek",
      lastFewNotes: ({ count }) => `Legutóbbi ${count} jegyzet`,
    },
    error: {
      title: "Nem található",
      notFound: "Ez a lap vagy privát vagy nem létezik.",
      home: "Vissza a kezdőlapra",
    },
    folderContent: {
      folder: "Mappa",
      itemsUnderFolder: ({ count }) => `Ebben a mappában ${count} elem található.`,
    },
    tagContent: {
      tag: "Címke",
      tagIndex: "Címke index",
      itemsUnderTag: ({ count }) => `${count} elem található ezzel a címkével.`,
      showingFirst: ({ count }) => `Első ${count} címke megjelenítve.`,
      totalTags: ({ count }) => `Összesen ${count} címke található.`,
    },
  },
} as const satisfies Translation
