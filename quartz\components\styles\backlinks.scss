@use "../../styles/variables.scss" as *;

.backlinks {
  flex-direction: column;
  padding: 1rem 0; // 增加容器的上下内边距
  /*&:after {
      pointer-events: none;
      content: "";
      width: 100%;
      height: 50px;
      position: absolute;
      left: 0;
      bottom: 0;
      opacity: 1;
      transition: opacity 0.3s ease;
      background: linear-gradient(transparent 0px, var(--light));
    }*/

  & > h3 {
    font-size: 1rem;
    margin: 0 0 0.75rem 0; // 增加标题的下边距
  }

  & > ul {
    list-style: none;
    padding: 0;
    margin: 0.75rem 0; // 增加列表的上下边距

    & > li {
      & > a {
        background-color: transparent;
      }
    }
  }

  & > .overflow {
    &:after {
      display: none;
    }
    height: auto;
    // @media all and not ($desktop) {
    //   height: 250px;
    // }
  }
}
