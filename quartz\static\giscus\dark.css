/*! MIT License
 * Copyright (c) 2018 GitHub Inc.
 * https://github.com/primer/primitives/blob/main/LICENSE
 */

main {
  --color-prettylights-syntax-comment: #8b949e;
  --color-prettylights-syntax-constant: #79c0ff;
  --color-prettylights-syntax-entity: #d2a8ff;
  --color-prettylights-syntax-storage-modifier-import: #c9d1d9;
  --color-prettylights-syntax-entity-tag: #7ee787;
  --color-prettylights-syntax-keyword: #ff7b72;
  --color-prettylights-syntax-string: #a5d6ff;
  --color-prettylights-syntax-variable: #ffa657;
  --color-prettylights-syntax-brackethighlighter-unmatched: #f85149;
  --color-prettylights-syntax-invalid-illegal-text: #f0f6fc;
  --color-prettylights-syntax-invalid-illegal-bg: #8e1519;
  --color-prettylights-syntax-carriage-return-text: #f0f6fc;
  --color-prettylights-syntax-carriage-return-bg: #b62324;
  --color-prettylights-syntax-string-regexp: #7ee787;
  --color-prettylights-syntax-markup-list: #f2cc60;
  --color-prettylights-syntax-markup-heading: #1f6feb;
  --color-prettylights-syntax-markup-italic: #c9d1d9;
  --color-prettylights-syntax-markup-bold: #c9d1d9;
  --color-prettylights-syntax-markup-deleted-text: #ffdcd7;
  --color-prettylights-syntax-markup-deleted-bg: #67060c;
  --color-prettylights-syntax-markup-inserted-text: #aff5b4;
  --color-prettylights-syntax-markup-inserted-bg: #033a16;
  --color-prettylights-syntax-markup-changed-text: #ffdfb6;
  --color-prettylights-syntax-markup-changed-bg: #5a1e02;
  --color-prettylights-syntax-markup-ignored-text: #c9d1d9;
  --color-prettylights-syntax-markup-ignored-bg: #1158c7;
  --color-prettylights-syntax-meta-diff-range: #d2a8ff;
  --color-prettylights-syntax-brackethighlighter-angle: #8b949e;
  --color-prettylights-syntax-sublimelinter-gutter-mark: #484f58;
  --color-prettylights-syntax-constant-other-reference-link: #a5d6ff;
  --color-btn-text: #d4d4d4; /* --darkgray */
  --color-btn-bg: #161618; /* --light */
  --color-btn-border: rgb(240, 246, 252 / 10%); /* --dark */
  --color-btn-shadow: 0 0 transparent;
  --color-btn-inset-shadow: 0 0 transparent;
  --color-btn-hover-bg: #30363d;
  --color-btn-hover-border: #8b949e;
  --color-btn-active-bg: hsl(212deg 12% 18% / 100%);
  --color-btn-active-border: #6e7681;
  --color-btn-selected-bg: #161b22;
  --color-btn-primary-text: #fff;
  --color-btn-primary-bg: #84a59d; /* --tertiary */
  --color-btn-primary-border: rgb(240, 246, 252 / 10%); /* --dark */
  --color-btn-primary-shadow: 0 0 transparent;
  --color-btn-primary-inset-shadow: 0 0 transparent;
  --color-btn-primary-hover-bg: #7b97aa; /* --secondary */
  --color-btn-primary-hover-border: rgb(240, 246, 252 / 10%); /* --dark */
  --color-btn-primary-selected-bg: #7b97aa; /* --secondary */
  --color-btn-primary-selected-shadow: 0 0 transparent;
  --color-btn-primary-disabled-text: rgba(33, 32, 32, 0.5);
  --color-btn-primary-disabled-bg: rgb(35 134 54 / 60%);
  --color-btn-primary-disabled-border: rgb(240 246 252 / 10%);
  --color-action-list-item-default-hover-bg: rgb(177 186 196 / 12%);
  --color-segmented-control-bg: rgb(110 118 129 / 10%);
  --color-segmented-control-button-bg: #0d1117;
  --color-segmented-control-button-selected-border: #6e7681;
  --color-fg-default: #ebebec; /* --dark */
  --color-fg-muted: #d4d4d4; /* --darkgray */
  --color-fg-subtle: #d4d4d4; /* --darkgray */
  --color-canvas-default: #0d1117;
  --color-canvas-overlay: #161b22;
  --color-canvas-inset: #010409;
  --color-canvas-subtle: #161b22;
  --color-border-default: #30363d;
  --color-border-muted: #21262d;
  --color-neutral-muted: rgb(110 118 129 / 40%);
  --color-accent-fg: #2f81f7;
  --color-accent-emphasis: #1f6feb;
  --color-accent-muted: rgb(56 139 253 / 40%);
  --color-accent-subtle: rgb(56 139 253 / 10%);
  --color-success-fg: #3fb950;
  --color-attention-fg: #d29922;
  --color-attention-muted: rgb(187 128 9 / 40%);
  --color-attention-subtle: rgb(187 128 9 / 15%);
  --color-danger-fg: #f85149;
  --color-danger-muted: rgb(248 81 73 / 40%);
  --color-danger-subtle: rgb(248 81 73 / 10%);
  --color-primer-shadow-inset: 0 0 transparent;
  --color-scale-gray-7: #21262d;
  --color-scale-blue-8: #0c2d6b;

  /*! Extensions from @primer/css/alerts/flash.scss */
  --color-social-reaction-bg-hover: var(--color-scale-gray-7);
  --color-social-reaction-bg-reacted-hover: var(--color-scale-blue-8);
}

main .pagination-loader-container {
  background-image: url("https://github.com/images/modules/pulls/progressive-disclosure-line-dark.svg");
}

main .gsc-loading-image {
  background-image: url("https://github.githubassets.com/images/mona-loading-dark.gif");
}
